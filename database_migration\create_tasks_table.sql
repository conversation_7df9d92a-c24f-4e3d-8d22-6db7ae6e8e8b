-- 创建任务表
CREATE TABLE `tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `project_id` bigint(20) DEFAULT NULL COMMENT '关联项目ID',
  `team_id` bigint(20) DEFAULT NULL COMMENT '关联团队ID',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `assignee_id` bigint(20) DEFAULT NULL COMMENT '分配给的用户ID',
  `status` enum('PUBLISHED','IN_PROGRESS','SUBMITTED','COMPLETED','CANCELLED') NOT NULL DEFAULT 'PUBLISHED' COMMENT '任务状态',
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') NOT NULL DEFAULT 'MEDIUM' COMMENT '优先级',
  `due_date` datetime DEFAULT NULL COMMENT '截止时间',
  `start_date` datetime DEFAULT NULL COMMENT '开始时间',
  `completion_date` datetime DEFAULT NULL COMMENT '完成时间',
  `estimated_hours` decimal(5,2) DEFAULT NULL COMMENT '预估工时',
  `actual_hours` decimal(5,2) DEFAULT NULL COMMENT '实际工时',
  `progress` int(11) DEFAULT 0 COMMENT '完成进度(0-100)',
  `attachments` text COMMENT '附件',
  `extra_data` json DEFAULT NULL COMMENT '扩展数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_assignee_id` (`assignee_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_due_date` (`due_date`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_tasks_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tasks_team` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tasks_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tasks_assignee` FOREIGN KEY (`assignee_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- 为关联表添加task_id字段
ALTER TABLE `records` ADD COLUMN `task_id` bigint(20) DEFAULT NULL COMMENT '关联任务ID';
ALTER TABLE `records` ADD KEY `idx_task_id` (`task_id`);
ALTER TABLE `records` ADD CONSTRAINT `fk_records_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `file_info` ADD COLUMN `task_id` bigint(20) DEFAULT NULL COMMENT '关联任务ID';
ALTER TABLE `file_info` ADD KEY `idx_task_id` (`task_id`);
ALTER TABLE `file_info` ADD CONSTRAINT `fk_file_info_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE SET NULL;
