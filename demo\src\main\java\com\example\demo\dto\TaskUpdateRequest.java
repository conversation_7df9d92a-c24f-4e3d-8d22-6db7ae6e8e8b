package com.example.demo.dto;

import com.example.demo.entity.Task;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 任务更新请求DTO
 */
@Data
public class TaskUpdateRequest {
    
    private String title;
    
    private String description;
    
    private Long projectId;
    
    private Long teamId;
    
    private Long assigneeId;
    
    private Task.TaskStatus status;
    
    private Task.Priority priority;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completionDate;
    
    @Min(value = 0, message = "预估工时不能小于0")
    private BigDecimal estimatedHours;
    
    @Min(value = 0, message = "实际工时不能小于0")
    private BigDecimal actualHours;
    
    @Min(value = 0, message = "进度不能小于0")
    @Max(value = 100, message = "进度不能大于100")
    private Integer progress;
    
    private String attachments;
    
    private String extraData;
    
    /**
     * 更新Task实体
     */
    public void updateTask(Task task) {
        if (this.title != null) {
            task.setTitle(this.title);
        }
        if (this.description != null) {
            task.setDescription(this.description);
        }
        if (this.projectId != null) {
            task.setProjectId(this.projectId);
        }
        if (this.teamId != null) {
            task.setTeamId(this.teamId);
        }
        if (this.assigneeId != null) {
            task.setAssigneeId(this.assigneeId);
        }
        if (this.status != null) {
            task.setStatus(this.status);
            // 如果状态变为完成，设置完成时间
            if (this.status == Task.TaskStatus.COMPLETED && task.getCompletionDate() == null) {
                task.setCompletionDate(LocalDateTime.now());
                task.setProgress(100);
            }
            // 如果状态变为进行中，设置开始时间
            if (this.status == Task.TaskStatus.IN_PROGRESS && task.getStartDate() == null) {
                task.setStartDate(LocalDateTime.now());
            }
        }
        if (this.priority != null) {
            task.setPriority(this.priority);
        }
        if (this.dueDate != null) {
            task.setDueDate(this.dueDate);
        }
        if (this.startDate != null) {
            task.setStartDate(this.startDate);
        }
        if (this.completionDate != null) {
            task.setCompletionDate(this.completionDate);
        }
        if (this.estimatedHours != null) {
            task.setEstimatedHours(this.estimatedHours);
        }
        if (this.actualHours != null) {
            task.setActualHours(this.actualHours);
        }
        if (this.progress != null) {
            task.setProgress(this.progress);
            // 如果进度达到100%，自动设置为完成状态
            if (this.progress == 100 && task.getStatus() != Task.TaskStatus.COMPLETED) {
                task.setStatus(Task.TaskStatus.COMPLETED);
                if (task.getCompletionDate() == null) {
                    task.setCompletionDate(LocalDateTime.now());
                }
            }
        }
        if (this.attachments != null) {
            task.setAttachments(this.attachments);
        }
        if (this.extraData != null) {
            task.setExtraData(this.extraData);
        }
    }
}
