package com.example.demo.service;

import com.example.demo.common.PageResult;
import com.example.demo.dto.TaskCreateRequest;
import com.example.demo.dto.TaskDTO;
import com.example.demo.dto.TaskUpdateRequest;
import com.example.demo.entity.Task;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务服务接口
 */
public interface TaskService {
    
    /**
     * 创建任务
     */
    Task createTask(Long userId, TaskCreateRequest request);
    
    /**
     * 更新任务
     */
    Task updateTask(Long taskId, Long userId, TaskUpdateRequest request);
    
    /**
     * 根据ID查找任务
     */
    Task findById(Long id);
    
    /**
     * 获取任务DTO
     */
    TaskDTO getTaskDTO(Long taskId);
    
    /**
     * 删除任务
     */
    void deleteTask(Long taskId, Long userId);
    
    /**
     * 分配任务
     */
    Task assignTask(Long taskId, Long assigneeId, Long operatorId);
    
    /**
     * 开始任务
     */
    Task startTask(Long taskId, Long userId);
    
    /**
     * 提交任务
     */
    Task submitTask(Long taskId, Long userId);
    
    /**
     * 完成任务
     */
    Task completeTask(Long taskId, Long userId);
    
    /**
     * 取消任务
     */
    Task cancelTask(Long taskId, Long userId);
    
    /**
     * 重新打开任务
     */
    Task reopenTask(Long taskId, Long userId);
    
    /**
     * 更新任务进度
     */
    Task updateProgress(Long taskId, Integer progress, Long userId);
    
    /**
     * 分页查询所有任务
     */
    PageResult<TaskDTO> findAllTasks(Pageable pageable);
    
    /**
     * 分页查询项目任务
     */
    PageResult<TaskDTO> findProjectTasks(Long projectId, Pageable pageable);
    
    /**
     * 分页查询团队任务
     */
    PageResult<TaskDTO> findTeamTasks(Long teamId, Pageable pageable);
    
    /**
     * 分页查询用户创建的任务
     */
    PageResult<TaskDTO> findUserCreatedTasks(Long userId, Pageable pageable);
    
    /**
     * 分页查询分配给用户的任务
     */
    PageResult<TaskDTO> findUserAssignedTasks(Long userId, Pageable pageable);
    
    /**
     * 分页查询用户相关的所有任务（创建的或分配的）
     */
    PageResult<TaskDTO> findUserTasks(Long userId, Pageable pageable);
    
    /**
     * 根据状态查询任务
     */
    PageResult<TaskDTO> findTasksByStatus(Task.TaskStatus status, Pageable pageable);
    
    /**
     * 根据优先级查询任务
     */
    PageResult<TaskDTO> findTasksByPriority(Task.Priority priority, Pageable pageable);
    
    /**
     * 搜索任务
     */
    PageResult<TaskDTO> searchTasks(String keyword, Task.TaskStatus status, Pageable pageable);
    
    /**
     * 获取即将到期的任务
     */
    List<TaskDTO> getTasksDueSoon(Long userId, int days);
    
    /**
     * 获取逾期任务
     */
    List<TaskDTO> getOverdueTasks(Long userId);
    
    /**
     * 获取用户的活跃任务
     */
    List<TaskDTO> getActiveTasksByUser(Long userId);
    
    /**
     * 获取项目的活跃任务
     */
    List<TaskDTO> getActiveTasksByProject(Long projectId);
    
    /**
     * 获取团队的活跃任务
     */
    List<TaskDTO> getActiveTasksByTeam(Long teamId);
    
    /**
     * 统计任务数量
     */
    long countTasks();
    
    /**
     * 统计项目任务数量
     */
    long countProjectTasks(Long projectId);
    
    /**
     * 统计团队任务数量
     */
    long countTeamTasks(Long teamId);
    
    /**
     * 统计用户任务数量
     */
    long countUserTasks(Long userId);
    
    /**
     * 统计用户特定状态的任务数量
     */
    long countUserTasksByStatus(Long userId, Task.TaskStatus status);
    
    /**
     * 检查用户是否有权限操作任务
     */
    boolean canOperateTask(Long taskId, Long userId);
    
    /**
     * 检查用户是否有权限删除任务
     */
    boolean canDeleteTask(Long taskId, Long userId);
    
    /**
     * 批量更新任务状态
     */
    void batchUpdateTaskStatus(List<Long> taskIds, Task.TaskStatus status, Long operatorId);
    
    /**
     * 批量分配任务
     */
    void batchAssignTasks(List<Long> taskIds, Long assigneeId, Long operatorId);
}
