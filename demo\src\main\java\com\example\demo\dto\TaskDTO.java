package com.example.demo.dto;

import com.example.demo.entity.Task;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务数据传输对象
 */
@Data
public class TaskDTO {
    
    private Long id;
    private String title;
    private String description;
    private Long projectId;
    private String projectName;
    private Long teamId;
    private String teamName;
    private Long creatorId;
    private String creatorName;
    private Long assigneeId;
    private String assigneeName;
    private Task.TaskStatus status;
    private String statusDescription;
    private Task.Priority priority;
    private String priorityDescription;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completionDate;
    
    private BigDecimal estimatedHours;
    private BigDecimal actualHours;
    private Integer progress;
    private String attachments;
    private String extraData;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // 扩展字段
    private boolean overdue;
    private boolean dueSoon;
    private long daysUntilDue;
    private List<String> attachmentList;
    
    /**
     * 从Task实体转换为DTO
     */
    public static TaskDTO fromTask(Task task) {
        if (task == null) {
            return null;
        }
        
        TaskDTO dto = new TaskDTO();
        dto.setId(task.getId());
        dto.setTitle(task.getTitle());
        dto.setDescription(task.getDescription());
        dto.setProjectId(task.getProjectId());
        dto.setTeamId(task.getTeamId());
        dto.setCreatorId(task.getCreatorId());
        dto.setAssigneeId(task.getAssigneeId());
        dto.setStatus(task.getStatus());
        dto.setStatusDescription(task.getStatus() != null ? task.getStatus().getDescription() : null);
        dto.setPriority(task.getPriority());
        dto.setPriorityDescription(task.getPriority() != null ? task.getPriority().getDescription() : null);
        dto.setDueDate(task.getDueDate());
        dto.setStartDate(task.getStartDate());
        dto.setCompletionDate(task.getCompletionDate());
        dto.setEstimatedHours(task.getEstimatedHours());
        dto.setActualHours(task.getActualHours());
        dto.setProgress(task.getProgress());
        dto.setAttachments(task.getAttachments());
        dto.setExtraData(task.getExtraData());
        dto.setCreateTime(task.getCreateTime());
        dto.setUpdateTime(task.getUpdateTime());
        
        // 设置关联实体名称
        if (task.getProject() != null) {
            dto.setProjectName(task.getProject().getName());
        }
        if (task.getTeam() != null) {
            dto.setTeamName(task.getTeam().getName());
        }
        if (task.getCreator() != null) {
            dto.setCreatorName(task.getCreator().getRealName());
        }
        if (task.getAssignee() != null) {
            dto.setAssigneeName(task.getAssignee().getRealName());
        }
        
        // 设置扩展字段
        dto.setOverdue(task.isOverdue());
        dto.setDueSoon(task.isDueSoon(7)); // 7天内到期
        
        if (task.getDueDate() != null) {
            long days = java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), task.getDueDate());
            dto.setDaysUntilDue(days);
        }
        
        // 处理附件列表
        if (task.getAttachments() != null && !task.getAttachments().trim().isEmpty()) {
            String[] attachments = task.getAttachments().split(";");
            dto.setAttachmentList(List.of(attachments));
        }
        
        return dto;
    }
    
    /**
     * 转换为Task实体（用于更新）
     */
    public Task toTask() {
        Task task = new Task();
        task.setId(this.id);
        task.setTitle(this.title);
        task.setDescription(this.description);
        task.setProjectId(this.projectId);
        task.setTeamId(this.teamId);
        task.setCreatorId(this.creatorId);
        task.setAssigneeId(this.assigneeId);
        task.setStatus(this.status);
        task.setPriority(this.priority);
        task.setDueDate(this.dueDate);
        task.setStartDate(this.startDate);
        task.setCompletionDate(this.completionDate);
        task.setEstimatedHours(this.estimatedHours);
        task.setActualHours(this.actualHours);
        task.setProgress(this.progress);
        task.setAttachments(this.attachments);
        task.setExtraData(this.extraData);
        return task;
    }
}
