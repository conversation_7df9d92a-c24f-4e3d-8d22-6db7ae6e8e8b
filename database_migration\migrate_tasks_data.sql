-- 数据迁移脚本：将records表中的任务数据迁移到tasks表

-- 第一步：迁移任务数据到tasks表
INSERT INTO `tasks` (
    `title`,
    `description`,
    `project_id`,
    `team_id`,
    `creator_id`,
    `assignee_id`,
    `status`,
    `priority`,
    `due_date`,
    `start_date`,
    `completion_date`,
    `estimated_hours`,
    `actual_hours`,
    `progress`,
    `attachments`,
    `extra_data`,
    `create_time`,
    `update_time`
)
SELECT 
    r.title,
    r.content as description,
    r.project_id,
    r.team_id,
    r.user_id as creator_id,
    NULL as assignee_id, -- 可以后续根据业务逻辑设置
    CASE 
        WHEN r.status = 'ACTIVE' THEN 'PUBLISHED'
        WHEN r.status = 'COMPLETED' THEN 'COMPLETED'
        WHEN r.status = 'SUBMITTED' THEN 'SUBMITTED'
        WHEN r.status = 'PUBLISHED' THEN 'PUBLISHED'
        ELSE 'PUBLISHED'
    END as status,
    r.priority,
    r.due_date,
    NULL as start_date, -- 可以设置为create_time
    CASE 
        WHEN r.status = 'COMPLETED' THEN r.update_time
        ELSE NULL
    END as completion_date,
    NULL as estimated_hours,
    NULL as actual_hours,
    CASE 
        WHEN r.status = 'COMPLETED' THEN 100
        WHEN r.status = 'SUBMITTED' THEN 90
        WHEN r.status = 'ACTIVE' OR r.status = 'PUBLISHED' THEN 0
        ELSE 0
    END as progress,
    r.attachments,
    r.extra_data,
    r.create_time,
    r.update_time
FROM records r
WHERE r.type = 'TASK';

-- 第二步：更新records表中SUBMISSION类型记录的task_id
-- 根据业务逻辑，提交记录可能关联到特定任务
-- 这里需要根据实际业务逻辑来设置关联关系
-- 示例：如果提交记录的title包含任务信息，可以通过匹配来建立关联

-- 第三步：更新file_info表中关联到任务的文件
UPDATE file_info fi
JOIN records r ON fi.record_id = r.id
JOIN tasks t ON t.title = r.title AND t.project_id = r.project_id AND t.team_id = r.team_id
SET fi.task_id = t.id
WHERE r.type = 'TASK';

-- 第四步：验证数据迁移结果
-- 检查迁移的任务数量
SELECT 
    '原records表中的任务数量' as description,
    COUNT(*) as count
FROM records 
WHERE type = 'TASK'
UNION ALL
SELECT 
    '新tasks表中的任务数量' as description,
    COUNT(*) as count
FROM tasks;

-- 检查关联关系
SELECT 
    '关联到任务的文件数量' as description,
    COUNT(*) as count
FROM file_info 
WHERE task_id IS NOT NULL;

-- 第五步：备份原始数据（可选）
-- CREATE TABLE records_backup AS SELECT * FROM records WHERE type = 'TASK';

-- 注意：在生产环境中执行前，请务必备份数据库！
