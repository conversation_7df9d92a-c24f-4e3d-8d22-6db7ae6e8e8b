package com.example.demo.repository;

import com.example.demo.entity.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务数据访问层
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {
    
    /**
     * 根据项目ID查找任务
     */
    Page<Task> findByProjectId(Long projectId, Pageable pageable);
    
    /**
     * 根据团队ID查找任务
     */
    Page<Task> findByTeamId(Long teamId, Pageable pageable);
    
    /**
     * 根据创建者ID查找任务
     */
    Page<Task> findByCreatorId(Long creatorId, Pageable pageable);
    
    /**
     * 根据分配者ID查找任务
     */
    Page<Task> findByAssigneeId(Long assigneeId, Pageable pageable);
    
    /**
     * 根据状态查找任务
     */
    Page<Task> findByStatus(Task.TaskStatus status, Pageable pageable);
    
    /**
     * 根据优先级查找任务
     */
    Page<Task> findByPriority(Task.Priority priority, Pageable pageable);
    
    /**
     * 根据项目ID和状态查找任务
     */
    Page<Task> findByProjectIdAndStatus(Long projectId, Task.TaskStatus status, Pageable pageable);
    
    /**
     * 根据团队ID和状态查找任务
     */
    Page<Task> findByTeamIdAndStatus(Long teamId, Task.TaskStatus status, Pageable pageable);
    
    /**
     * 根据分配者ID和状态查找任务
     */
    Page<Task> findByAssigneeIdAndStatus(Long assigneeId, Task.TaskStatus status, Pageable pageable);
    
    /**
     * 查找即将到期的任务
     */
    @Query("SELECT t FROM Task t WHERE t.dueDate BETWEEN :startDate AND :endDate " +
           "AND t.status NOT IN ('COMPLETED', 'CANCELLED') " +
           "AND (t.assigneeId = :userId OR t.creatorId = :userId)")
    List<Task> findTasksDueSoon(@Param("userId") Long userId, 
                               @Param("startDate") LocalDateTime startDate,
                               @Param("endDate") LocalDateTime endDate);
    
    /**
     * 查找逾期任务
     */
    @Query("SELECT t FROM Task t WHERE t.dueDate < :currentDate " +
           "AND t.status NOT IN ('COMPLETED', 'CANCELLED') " +
           "AND (t.assigneeId = :userId OR t.creatorId = :userId)")
    List<Task> findOverdueTasks(@Param("userId") Long userId, 
                               @Param("currentDate") LocalDateTime currentDate);
    
    /**
     * 根据关键词搜索任务
     */
    @Query("SELECT t FROM Task t WHERE " +
           "(t.title LIKE %:keyword% OR t.description LIKE %:keyword%) " +
           "ORDER BY t.createTime DESC")
    Page<Task> searchTasks(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据关键词和状态搜索任务
     */
    @Query("SELECT t FROM Task t WHERE " +
           "(t.title LIKE %:keyword% OR t.description LIKE %:keyword%) " +
           "AND t.status = :status " +
           "ORDER BY t.createTime DESC")
    Page<Task> searchTasksByStatus(@Param("keyword") String keyword, 
                                  @Param("status") Task.TaskStatus status, 
                                  Pageable pageable);
    
    /**
     * 统计项目任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.projectId = :projectId")
    long countByProjectId(@Param("projectId") Long projectId);
    
    /**
     * 统计团队任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.teamId = :teamId")
    long countByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 统计用户任务数量（创建的或分配的）
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.creatorId = :userId OR t.assigneeId = :userId")
    long countByUserId(@Param("userId") Long userId);
    
    /**
     * 统计用户特定状态的任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE " +
           "(t.creatorId = :userId OR t.assigneeId = :userId) " +
           "AND t.status = :status")
    long countByUserIdAndStatus(@Param("userId") Long userId, 
                               @Param("status") Task.TaskStatus status);
    
    /**
     * 查找用户的活跃任务（未完成且未取消）
     */
    @Query("SELECT t FROM Task t WHERE " +
           "(t.creatorId = :userId OR t.assigneeId = :userId) " +
           "AND t.status NOT IN ('COMPLETED', 'CANCELLED') " +
           "ORDER BY t.dueDate ASC, t.priority DESC")
    List<Task> findActiveTasksByUser(@Param("userId") Long userId);
    
    /**
     * 查找项目的活跃任务
     */
    @Query("SELECT t FROM Task t WHERE t.projectId = :projectId " +
           "AND t.status NOT IN ('COMPLETED', 'CANCELLED') " +
           "ORDER BY t.dueDate ASC, t.priority DESC")
    List<Task> findActiveTasksByProject(@Param("projectId") Long projectId);
    
    /**
     * 查找团队的活跃任务
     */
    @Query("SELECT t FROM Task t WHERE t.teamId = :teamId " +
           "AND t.status NOT IN ('COMPLETED', 'CANCELLED') " +
           "ORDER BY t.dueDate ASC, t.priority DESC")
    List<Task> findActiveTasksByTeam(@Param("teamId") Long teamId);
}
