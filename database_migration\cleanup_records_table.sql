-- 最终清理脚本：在完全迁移到新的任务管理系统后执行
-- 注意：此脚本应在确认新系统运行正常后执行，且执行前务必备份数据库！

-- 第一步：删除records表中的任务数据
-- 在执行前，请确认所有任务数据已成功迁移到tasks表
DELETE FROM records WHERE type = 'TASK';

-- 第二步：修改records表的type枚举，移除TASK选项
-- 注意：这个操作会重建表，请在低峰期执行
ALTER TABLE records MODIFY COLUMN type enum('DISCUSSION','SUBMISSION','ANNOUNCEMENT','FEEDBACK','EVALUATION_ITEM') NOT NULL COMMENT '记录类型';

-- 第三步：验证清理结果
-- 检查records表中是否还有TASK类型的记录
SELECT COUNT(*) as remaining_task_records FROM records WHERE type = 'TASK';

-- 检查records表的类型分布
SELECT type, COUNT(*) as count 
FROM records 
GROUP BY type 
ORDER BY count DESC;

-- 检查任务关联的记录数量
SELECT 
    'SUBMISSION关联到任务的数量' as description,
    COUNT(*) as count
FROM records 
WHERE type = 'SUBMISSION' AND task_id IS NOT NULL
UNION ALL
SELECT 
    'DISCUSSION关联到任务的数量' as description,
    COUNT(*) as count
FROM records 
WHERE type = 'DISCUSSION' AND task_id IS NOT NULL;

-- 第四步：优化索引（可选）
-- 由于移除了TASK类型，可以考虑重新优化索引
-- 分析表的查询模式，调整索引策略

-- 第五步：更新统计信息
ANALYZE TABLE records;
ANALYZE TABLE tasks;

-- 验证外键约束
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
AND (TABLE_NAME = 'records' OR TABLE_NAME = 'tasks' OR TABLE_NAME = 'file_info')
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;
