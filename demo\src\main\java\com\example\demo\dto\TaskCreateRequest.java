package com.example.demo.dto;

import com.example.demo.entity.Task;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 任务创建请求DTO
 */
@Data
public class TaskCreateRequest {
    
    @NotBlank(message = "任务标题不能为空")
    private String title;
    
    private String description;
    
    private Long projectId;
    
    private Long teamId;
    
    private Long assigneeId;
    
    @NotNull(message = "任务状态不能为空")
    private Task.TaskStatus status = Task.TaskStatus.PUBLISHED;
    
    @NotNull(message = "优先级不能为空")
    private Task.Priority priority = Task.Priority.MEDIUM;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;
    
    @Min(value = 0, message = "预估工时不能小于0")
    private BigDecimal estimatedHours;
    
    @Min(value = 0, message = "进度不能小于0")
    @Max(value = 100, message = "进度不能大于100")
    private Integer progress = 0;
    
    private String attachments;
    
    private String extraData;
    
    /**
     * 转换为Task实体
     */
    public Task toTask(Long creatorId) {
        Task task = new Task();
        task.setTitle(this.title);
        task.setDescription(this.description);
        task.setProjectId(this.projectId);
        task.setTeamId(this.teamId);
        task.setCreatorId(creatorId);
        task.setAssigneeId(this.assigneeId);
        task.setStatus(this.status);
        task.setPriority(this.priority);
        task.setDueDate(this.dueDate);
        task.setStartDate(this.startDate);
        task.setEstimatedHours(this.estimatedHours);
        task.setProgress(this.progress != null ? this.progress : 0);
        task.setAttachments(this.attachments);
        task.setExtraData(this.extraData);
        return task;
    }
}
