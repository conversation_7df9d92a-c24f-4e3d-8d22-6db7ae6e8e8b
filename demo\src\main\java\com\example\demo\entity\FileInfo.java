package com.example.demo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 文件信息实体类
 * 用于管理上传的文件信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "file_info")
public class FileInfo {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "原始文件名不能为空")
    @Column(name = "original_name", nullable = false, length = 255)
    private String originalName;
    
    @NotBlank(message = "存储文件名不能为空")
    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;
    
    @NotBlank(message = "文件路径不能为空")
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;
    
    @NotNull(message = "文件大小不能为空")
    @Column(name = "file_size", nullable = false)
    private Long fileSize;
    
    @Column(name = "content_type", length = 100)
    private String contentType;
    
    @Column(name = "file_extension", length = 20)
    private String fileExtension;
    
    @NotNull(message = "上传用户不能为空")
    @Column(name = "uploader_id", nullable = false)
    private Long uploaderId;
    
    @Column(name = "project_id")
    private Long projectId;
    
    @Column(name = "team_id")
    private Long teamId;
    
    @Column(name = "record_id")
    private Long recordId;

    @Column(name = "task_id")
    private Long taskId;

    @Enumerated(EnumType.STRING)
    @Column(name = "file_type", nullable = false)
    private FileType fileType = FileType.DOCUMENT;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private FileStatus status = FileStatus.ACTIVE;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "download_count")
    private Integer downloadCount = 0;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @CreationTimestamp
    @Column(name = "upload_time", nullable = false, updatable = false)
    private LocalDateTime uploadTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    // 关联的用户（上传者）
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "uploader_id", insertable = false, updatable = false)
    private User uploader;
    
    // 关联的项目
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    private Project project;
    
    // 关联的团队
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "team_id", insertable = false, updatable = false)
    private Team team;
    
    // 关联的记录
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "record_id", insertable = false, updatable = false)
    private Record record;

    // 关联的任务
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id", insertable = false, updatable = false)
    private Task task;
    
    /**
     * 文件类型枚举
     */
    public enum FileType {
        DOCUMENT("文档"),
        IMAGE("图片"),
        VIDEO("视频"),
        AUDIO("音频"),
        ARCHIVE("压缩包"),
        CODE("代码"),
        ATTACHMENT("附件"),
        OTHER("其他");
        
        private final String description;
        
        FileType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 文件状态枚举
     */
    public enum FileStatus {
        ACTIVE("正常"),
        DELETED("已删除"),
        QUARANTINE("隔离");
        
        private final String description;
        
        FileStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取文件大小的可读格式
     */
    public String getReadableFileSize() {
        if (fileSize == null) return "0B";
        
        long size = fileSize;
        if (size < 1024) return size + "B";
        if (size < 1024 * 1024) return String.format("%.1fKB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1fMB", size / (1024.0 * 1024));
        return String.format("%.1fGB", size / (1024.0 * 1024 * 1024));
    }
    
    /**
     * 检查是否为图片文件
     */
    public boolean isImage() {
        return FileType.IMAGE.equals(this.fileType) || 
               (contentType != null && contentType.startsWith("image/"));
    }
    
    /**
     * 检查是否为文档文件
     */
    public boolean isDocument() {
        return FileType.DOCUMENT.equals(this.fileType) ||
               (contentType != null && (
                   contentType.contains("pdf") ||
                   contentType.contains("word") ||
                   contentType.contains("excel") ||
                   contentType.contains("powerpoint") ||
                   contentType.contains("text")
               ));
    }
}
