package com.example.demo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 多功能记录实体类
 * 一表多用：任务/讨论/提交/评价/公告
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "records")
public class Record {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "记录类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RecordType type;
    
    @NotBlank(message = "标题不能为空")
    @Column(nullable = false, length = 200)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String content;
    
    @Column(name = "project_id")
    private Long projectId;
    
    @Column(name = "team_id")
    private Long teamId;
    
    @NotNull(message = "创建用户不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "task_id")
    private Long taskId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RecordStatus status = RecordStatus.ACTIVE;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Priority priority = Priority.MEDIUM;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "due_date")
    private LocalDateTime dueDate;
    
    @Column(name = "extra_data", columnDefinition = "JSON")
    private String extraData;
    
    @Column(columnDefinition = "TEXT")
    private String attachments;
    
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    // 关联项目信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    private Project project;

    // 关联团队信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "team_id", insertable = false, updatable = false)
    private Team team;

    // 关联用户信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    // 父记录信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Record parent;

    // 关联任务信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id", insertable = false, updatable = false)
    private Task task;
    
    /**
     * 记录类型枚举
     */
    public enum RecordType {
        TASK("任务"),
        DISCUSSION("讨论"),
        SUBMISSION("提交"),
        ANNOUNCEMENT("公告"),
        FEEDBACK("反馈"),
        EVALUATION_ITEM("评价项目");

        private final String description;

        RecordType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW("低"),
        MEDIUM("中"),
        HIGH("高"),
        URGENT("紧急");
        
        private final String description;
        
        Priority(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 记录状态枚举
     */
    public enum RecordStatus {
        ACTIVE("活跃"),
        PUBLISHED("已发布"),
        IN_PROGRESS("进行中"),
        SUBMITTED("已提交"),
        COMPLETED("已完成"),
        CANCELLED("已取消"),
        REJECTED("已拒绝"),
        APPROVED("已通过"),
        DRAFT("草稿");

        private final String description;

        RecordStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 判断是否为任务
     */
    public boolean isTask() {
        return RecordType.TASK.equals(this.type);
    }
    
    /**
     * 判断是否为讨论
     */
    public boolean isDiscussion() {
        return RecordType.DISCUSSION.equals(this.type);
    }
    
    /**
     * 判断是否为提交
     */
    public boolean isSubmission() {
        return RecordType.SUBMISSION.equals(this.type);
    }
    
    /**
     * 判断是否为评价项目
     */
    public boolean isEvaluationItem() {
        return RecordType.EVALUATION_ITEM.equals(this.type);
    }

    /**
     * 判断是否为反馈
     */
    public boolean isFeedback() {
        return RecordType.FEEDBACK.equals(this.type);
    }
    
    /**
     * 判断是否为回复
     */
    public boolean isReply() {
        return this.parentId != null;
    }
}
